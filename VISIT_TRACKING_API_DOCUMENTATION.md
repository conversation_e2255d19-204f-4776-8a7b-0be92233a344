# Visit Tracking API Documentation

## Overview

The Visit Tracking API provides functionality to track and increment visit counts for content items. This system uses atomic database operations to ensure accurate counting and includes comprehensive logging for analytics purposes.

## Base URL

```
http://your-domain.com/api/v1
```

## Authentication

Currently, no authentication is required for this endpoint.

## Endpoint

### Increment Content Visit Count

Increments the visit count for a specific content item by 1 and returns the updated content data.

**Endpoint:** `POST /api/v1/content/{id}/visit`

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | integer | Yes | The unique identifier of the content item |

#### Request Body

No request body is required for this endpoint.

#### Response Format

**Success Response (200):**

```json
{
  "id": 1,
  "title": "درس دروس القرآن الكريم 1",
  "description": "وصف تفصيلي للدرس رقم 1 في دروس القرآن الكريم",
  "content_type_label": "صوتي",
  "content_type": "audio",
  "visits": 4,
  "audio_data": {
    "id": 1,
    "created_at": "2025-06-19T19:20:11.000000Z",
    "updated_at": "2025-06-19T19:20:11.000000Z",
    "audio_file": "audio/7893ac95-f7a5-39c8-bac1-06a9130d3faf.mp3",
    "audio_url": "http://127.0.0.1:8000/uploads/audio/7893ac95-f7a5-39c8-bac1-06a9130d3faf.mp3",
    "image": "audio/thumbnails/80c78fb8-0094-3e73-9ac7-a9a75b542f36.jpg",
    "image_url": "http://127.0.0.1:8000/uploads/audio/thumbnails/80c78fb8-0094-3e73-9ac7-a9a75b542f36.jpg",
    "duration": "00:08:15"
  },
  "category_id": 7,
  "created_at": "2025-06-19T19:20:11.000000Z",
  "updated_at": "2025-06-20T16:54:17.000000Z"
}
```

#### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Unique content identifier |
| `title` | string | Content title |
| `description` | string\|null | Content description |
| `content_type_label` | string | Human-readable content type |
| `content_type` | string | Polymorphic type identifier (audio, video, book, etc.) |
| `visits` | integer | **NEW** - Current visit count after increment |
| `{type}_data` | object | Polymorphic content data based on type |
| `category_id` | integer | Associated category ID |
| `created_at` | string | ISO 8601 timestamp of creation |
| `updated_at` | string | ISO 8601 timestamp of last update |

## Example Requests

### Basic Visit Tracking

```bash
curl -X POST "http://your-domain.com/api/v1/content/1/visit" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json"
```

### JavaScript/Fetch Example

```javascript
fetch('http://your-domain.com/api/v1/content/1/visit', {
  method: 'POST',
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(content => {
  console.log('Visit tracked! New count:', content.visits);
  console.log('Content:', content.title);
});
```

### React Hook Example

```javascript
const useVisitTracker = () => {
  const trackVisit = async (contentId) => {
    try {
      const response = await fetch(`/api/v1/content/${contentId}/visit`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const content = await response.json();
        return content.visits;
      }
    } catch (error) {
      console.error('Failed to track visit:', error);
    }
    return null;
  };

  return { trackVisit };
};
```

## Error Responses

### Content Not Found (404)

```json
{
  "error": "Content not found or inactive"
}
```

### Server Error (500)

```json
{
  "error": "An error occurred while tracking the visit"
}
```

### Failed to Update (500)

```json
{
  "error": "Failed to update visit count"
}
```

## Database Schema Changes

### New Field Added to `contents` Table

| Field | Type | Default | Description |
|-------|------|---------|-------------|
| `visits` | unsigned integer | 0 | Number of times this content has been visited |

### New Indexes Added

- `contents_visits_index` - For sorting by visit count
- `contents_status_visits_index` - For filtering active content by visits

## Model Enhancements

### New Methods in Content Model

```php
// Safely increment visits using atomic operations
$content->incrementVisits(); // Returns boolean

// Get current visit count
$visitCount = $content->getVisitsCount(); // Returns integer

// Query scopes for sorting
Content::mostVisited()->get(); // Order by visits DESC
Content::leastVisited()->get(); // Order by visits ASC
```

## Features

- ✅ **Atomic Operations**: Uses database increment() for race condition safety
- ✅ **Active Content Only**: Only tracks visits for active content in active categories
- ✅ **Comprehensive Logging**: Logs visit details for analytics
- ✅ **Error Handling**: Graceful error handling with appropriate HTTP status codes
- ✅ **Clean Response**: Returns full content data with updated visit count
- ✅ **Performance Optimized**: Database indexes for efficient querying
- ✅ **Consistent API**: Follows established response patterns
- ✅ **Analytics Ready**: Logs IP, user agent, and timestamp for analysis

## Analytics Logging

Each visit is logged with the following information:
- Content ID and title
- Content type and category
- New visit count
- User IP address
- User agent string
- Timestamp

## Integration with Existing APIs

The `visits` field is now included in all content API responses:
- `GET /api/v1/categories/{id}/contents` - Shows visit counts for all content
- `GET /api/v1/content/search` - Includes visit counts in search results
- `POST /api/v1/content/{id}/visit` - Returns updated content with new visit count

## Performance Considerations

- Uses atomic `increment()` operations to prevent race conditions
- Database indexes on `visits` field for efficient sorting
- Composite index on `status` and `visits` for filtered queries
- Minimal overhead - single database query per visit

## Security Considerations

- Only active content in active categories can be visited
- No authentication required (public content tracking)
- Rate limiting can be added at the web server level if needed
- IP and user agent logging for abuse detection

## Use Cases

- **Content Analytics**: Track most popular content
- **Recommendation Systems**: Suggest trending content
- **Performance Metrics**: Measure content engagement
- **A/B Testing**: Compare content performance
- **User Behavior Analysis**: Understand content consumption patterns
