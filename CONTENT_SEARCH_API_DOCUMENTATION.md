# Content Search API Documentation

## Overview

The Content Search API provides a powerful search functionality across all content types (Audio, Video, Book, Article, Post) and their associated categories. The search operates on content titles, descriptions, and type-specific fields, returning results grouped by category with full polymorphic content data.

## Base URL

```
http://your-domain.com/api/v1
```

## Authentication

Currently, no authentication is required for this endpoint.

## Endpoint

### Search Content

Search for content across all categories and content types using a query string.

**Endpoint:** `GET /api/v1/content/search`

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `query` | string | Yes | Search term (1-255 characters) |

#### Search Fields

The search functionality covers the following fields:

**Content Table:**
- `title` - Content title
- `description` - Content description

**Polymorphic Content Types:**
- **Audio**: `audio_file` field
- **Video**: `youtube_video_id` field  
- **Book**: `publisher` field
- **Article**: `text` field
- **Post**: `text` and `url` fields

#### Response Format

```json
{
  "search_query": "search term",
  "total_results": 14,
  "categories_with_results": 5,
  "content_type_counts": {
    "audio": 12,
    "video": 2
  },
  "results": [
    {
      "category": {
        "id": 7,
        "name": "Category Name",
        "parent_id": 1,
        "subcategories_count": 0,
        "subcategories": []
      },
      "contents": [
        {
          "id": 1,
          "title": "Content Title",
          "description": "Content Description",
          "content_type_label": "Audio",
          "content_type": "audio",
          "audio_data": {
            "id": 1,
            "audio_file": "audio/file.mp3",
            "audio_url": "https://domain.com/uploads/audio/file.mp3",
            "image": "audio/thumbnails/thumb.jpg",
            "image_url": "https://domain.com/uploads/audio/thumbnails/thumb.jpg",
            "duration": "00:08:15",
            "created_at": "2025-06-19T19:20:11.000000Z",
            "updated_at": "2025-06-19T19:20:11.000000Z"
          },
          "category_id": 7,
          "created_at": "2025-06-19T19:20:11.000000Z",
          "updated_at": "2025-06-19T19:20:11.000000Z"
        }
      ],
      "content_count": 3
    }
  ]
}
```

#### Response Fields

**Root Level:**
- `search_query` - The search term used
- `total_results` - Total number of content items found
- `categories_with_results` - Number of categories containing results
- `content_type_counts` - Count of results by content type
- `results` - Array of category groups with their content

**Category Object:**
- `id` - Category ID
- `name` - Category name
- `parent_id` - Parent category ID (null for main categories)
- `subcategories_count` - Number of subcategories
- `subcategories` - Array of immediate subcategories

**Content Object:**
- `id` - Content ID
- `title` - Content title
- `description` - Content description
- `content_type_label` - Human-readable content type
- `content_type` - Polymorphic type identifier (audio, video, book, article, post)
- `{type}_data` - Polymorphic content data based on type
- `category_id` - Associated category ID
- `created_at` - Creation timestamp
- `updated_at` - Last update timestamp

#### Polymorphic Content Data

**Audio Data:**
- `audio_file` - Audio file path
- `audio_url` - Full audio file URL
- `image` - Thumbnail image path
- `image_url` - Full thumbnail URL
- `duration` - Audio duration

**Video Data:**
- `youtube_video_id` - YouTube video ID
- `duration` - Video duration
- `embed_url` - YouTube embed URL
- `watch_url` - YouTube watch URL
- `thumbnail_url` - YouTube thumbnail URL
- `thumbnail_medium_url` - YouTube medium thumbnail URL

**Book Data:**
- `pages_count` - Number of pages
- `published_date` - Publication date
- `publisher` - Publisher name
- `cover_image` - Cover image path
- `cover_image_url` - Full cover image URL
- `file` - Book file path
- `file_url` - Full book file URL

**Article Data:**
- `text` - Article text content
- `image` - Article image path
- `image_url` - Full article image URL

**Post Data:**
- `text` - Post text content
- `image` - Post image path
- `image_url` - Full post image URL
- `url` - Associated URL
- `published_at` - Publication timestamp

## Example Requests

### Basic Search

```bash
curl -X GET "http://your-domain.com/api/v1/content/search?query=lesson" \
  -H "Accept: application/json"
```

### Arabic Search

```bash
curl -X GET "http://your-domain.com/api/v1/content/search" \
  -G --data-urlencode "query=درس" \
  -H "Accept: application/json"
```

## Error Responses

### Validation Error (422)

```json
{
  "errors": {
    "query": [
      "The query field is required."
    ]
  }
}
```

### Empty Query (422)

```json
{
  "error": "Search query cannot be empty"
}
```

### Server Error (500)

```json
{
  "error": "An error occurred while searching content"
}
```

## Features

- ✅ **Multi-language Support**: Handles Arabic, English, and other character sets
- ✅ **Polymorphic Search**: Searches across all content types with type-specific fields
- ✅ **Category Grouping**: Results organized by category with hierarchical information
- ✅ **Content Type Filtering**: Shows distribution of results by content type
- ✅ **Full Content Data**: Includes complete polymorphic content information
- ✅ **Active Content Only**: Returns only active categories and content items
- ✅ **Comprehensive Coverage**: Searches titles, descriptions, and type-specific fields
- ✅ **Clean Response Structure**: No wrapper fields, following established API patterns
- ✅ **Error Handling**: Appropriate HTTP status codes and clear error messages

## Performance Considerations

- The search uses LIKE queries with wildcards for flexible matching
- Results are ordered by creation date (newest first)
- Only active categories and content are included in results
- Eager loading is used to prevent N+1 query issues
- Search is case-insensitive for better user experience

## Integration Notes

This endpoint follows the same patterns as other API endpoints in the system:
- Uses the same resource classes (CategoryResource, ApiContentResource)
- Maintains consistency with existing response structures
- Leverages the polymorphic morph map configuration
- Respects the active status filtering used throughout the application
