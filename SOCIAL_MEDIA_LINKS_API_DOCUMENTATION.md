# Social Media Links API Documentation

## Overview

The Social Media Links API provides an endpoint for retrieving active social media links for the application. This endpoint returns a clean list of social media platforms with their associated URLs, icons, and display information, ordered by the sort field.

## Base URL

```
http://your-domain.com/api/v1
```

## Authentication

Currently, no authentication is required for this endpoint.

## Endpoint

### Get Social Media Links

Retrieves all active social media links ordered by their sort field.

**Endpoint:** `GET /api/v1/social-media-links`

#### Query Parameters

None required.

#### Response Format

```json
[
  {
    "id": 1,
    "key": "facebook",
    "name": "Facebook",
    "icon": "icons/facebook.svg",
    "icon_url": "http://your-domain.com/uploads/icons/facebook.svg",
    "url": "https://facebook.com/alfarih",
    "sort": 1,
    "active": true,
    "created_at": "2025-06-20T11:37:14.000000Z",
    "updated_at": "2025-06-20T11:37:14.000000Z"
  },
  {
    "id": 2,
    "key": "twitter",
    "name": "Twitter",
    "icon": "icons/twitter.svg",
    "icon_url": "http://your-domain.com/uploads/icons/twitter.svg",
    "url": "https://twitter.com/alfarih",
    "sort": 2,
    "active": true,
    "created_at": "2025-06-20T11:37:14.000000Z",
    "updated_at": "2025-06-20T11:37:14.000000Z"
  }
]
```

#### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Unique social media link identifier |
| `key` | string | Unique key identifier (e.g., 'facebook', 'twitter') |
| `name` | string | Display name of the social media platform |
| `icon` | string | Path to the icon file (nullable) |
| `icon_url` | string | Full URL to the icon file (nullable) |
| `url` | string | Social media profile URL |
| `sort` | integer | Sort order for displaying links |
| `active` | boolean | Whether the link is active |
| `created_at` | string | ISO 8601 timestamp of creation |
| `updated_at` | string | ISO 8601 timestamp of last update |

## Example Requests

### Basic Request

```bash
curl -X GET "http://your-domain.com/api/v1/social-media-links" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json"
```

### JavaScript/Fetch Example

```javascript
fetch('http://your-domain.com/api/v1/social-media-links', {
  method: 'GET',
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(socialMediaLinks => {
  console.log('Social Media Links:', socialMediaLinks);
  
  // Example: Create social media icons
  socialMediaLinks.forEach(link => {
    console.log(`${link.name}: ${link.url}`);
  });
});
```

## Error Responses

### Server Error (500)

```json
{
  "error": "An error occurred while retrieving social media links"
}
```

## Features

- ✅ **Active Links Only**: Returns only active social media links
- ✅ **Ordered Results**: Links are ordered by the sort field (ascending)
- ✅ **Icon Support**: Includes both icon path and full icon URL
- ✅ **Clean Response**: No wrapper fields, following established API patterns
- ✅ **Error Handling**: Comprehensive error logging and user-friendly messages
- ✅ **Flexible Keys**: Supports any social media platform with unique key identifiers
- ✅ **Admin Management**: Full Filament admin interface for managing links

## Data Model

### SocialMediaLink Model Fields

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| `key` | string | max:50, unique | Unique identifier (e.g., 'facebook') |
| `name` | string | max:100 | Display name (e.g., 'Facebook') |
| `icon` | string | nullable, max:255 | Icon file path |
| `url` | string | max:500, valid URL | Social media profile URL |
| `sort` | integer | 0-999 | Sort order for display |
| `active` | boolean | default: true | Enable/disable the link |

### Validation Rules

- **Key**: Required, unique, lowercase alphanumeric with hyphens/underscores
- **Name**: Required, human-readable display name
- **Icon**: Optional, SVG/PNG/JPEG files up to 1MB
- **URL**: Required, valid URL format
- **Sort**: Integer between 0-999, defaults to 0
- **Active**: Boolean, defaults to true

## Admin Management

The social media links can be managed through the Filament admin interface:

- **Location**: Admin Panel → Settings → Social Media Links
- **Features**: Create, edit, delete, bulk operations
- **Sorting**: Drag-and-drop or manual sort order
- **File Upload**: Icon upload with preview
- **Validation**: Real-time validation with helpful error messages
- **Filtering**: Filter by active/inactive status

## Integration Notes

This endpoint follows the same patterns as other API endpoints in the system:
- Uses the established resource class pattern (SocialMediaLinkResource)
- Maintains consistency with existing response structures
- Follows the same error handling and logging patterns
- Respects the active status filtering used throughout the application
- Uses the same URL generation patterns for file uploads

## Use Cases

- **Website Footer**: Display social media icons with links
- **Contact Pages**: Show available social media channels
- **Mobile Apps**: Populate social media sharing options
- **Email Templates**: Include social media links in communications
- **Marketing Materials**: Consistent social media presence across platforms
