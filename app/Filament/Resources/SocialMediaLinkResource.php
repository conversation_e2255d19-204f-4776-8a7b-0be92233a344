<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SocialMediaLinkResource\Pages;
use App\Models\SocialMediaLink;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class SocialMediaLinkResource extends Resource
{
    protected static ?string $model = SocialMediaLink::class;

    protected static ?string $navigationIcon = 'heroicon-o-share';

    public static function getNavigationGroup(): ?string
    {
        return __('settings');
    }

    protected static ?int $navigationSort = 10;

    public static function getNavigationLabel(): string
    {
        return __('social_media_links');
    }

    public static function getModelLabel(): string
    {
        return __('social_media_link');
    }

    public static function getPluralModelLabel(): string
    {
        return __('social_media_link_plural');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('key')
                    ->label(__('key'))
                    ->placeholder(__('social_media_key_placeholder'))
                    ->helperText(__('social_media_key_help'))
                    ->required()
                    ->maxLength(50)
                    ->unique(SocialMediaLink::class, 'key', ignoreRecord: true)
                    ->regex('/^[a-z0-9_-]+$/')
                    ->validationMessages([
                        'regex' => __('social_media_key_format_error'),
                    ]),
                Forms\Components\TextInput::make('name')
                    ->label(__('name'))
                    ->placeholder(__('social_media_name_placeholder'))
                    ->helperText(__('social_media_name_help'))
                    ->required()
                    ->maxLength(100),
                Forms\Components\FileUpload::make('icon')
                    ->label(__('icon'))
                    ->helperText(__('social_media_icon_help'))
                    ->directory('icons')
                    ->acceptedFileTypes(['image/svg+xml', 'image/png', 'image/jpeg'])
                    ->maxSize(1024),
                Forms\Components\TextInput::make('url')
                    ->label(__('url'))
                    ->placeholder(__('social_media_url_placeholder'))
                    ->helperText(__('social_media_url_help'))
                    ->required()
                    ->url()
                    ->maxLength(500),
                Forms\Components\TextInput::make('sort')
                    ->label(__('sort_order'))
                    ->helperText(__('social_media_sort_help'))
                    ->numeric()
                    ->default(0)
                    ->minValue(0)
                    ->maxValue(999),
                Forms\Components\Toggle::make('active')
                    ->label(__('active'))
                    ->helperText(__('social_media_active_help'))
                    ->default(true),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('id'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('key')
                    ->label(__('key'))
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('primary'),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('name'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\ImageColumn::make('icon')
                    ->label(__('icon'))
                    ->disk('public')
                    ->height(30)
                    ->width(30),
                Tables\Columns\TextColumn::make('url')
                    ->label(__('url'))
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    })
                    ->url(fn($record) => $record->url, shouldOpenInNewTab: true),
                Tables\Columns\TextColumn::make('sort')
                    ->label(__('sort_order'))
                    ->sortable()
                    ->badge()
                    ->color('secondary'),
                Tables\Columns\IconColumn::make('active')
                    ->label(__('active'))
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('active')
                    ->label(__('active'))
                    ->placeholder(__('all'))
                    ->trueLabel(__('active_only'))
                    ->falseLabel(__('inactive_only')),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->modalHeading(__('delete_confirmation'))
                    ->successNotificationTitle(__('social_media_link_deleted')),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->modalHeading(__('bulk_delete_confirmation'))
                        ->successNotificationTitle(__('social_media_links_bulk_deleted')),
                ]),
            ])
            ->defaultSort('sort', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSocialMediaLinks::route('/'),
            'create' => Pages\CreateSocialMediaLink::route('/create'),
            'edit' => Pages\EditSocialMediaLink::route('/{record}/edit'),
        ];
    }
}
