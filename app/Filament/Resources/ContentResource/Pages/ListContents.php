<?php

namespace App\Filament\Resources\ContentResource\Pages;

use App\Filament\Resources\ContentResource;
use App\Models\Category;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;

class ListContents extends ListRecords
{
    protected static string $resource = ContentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        $tabs = [];
        $mainCategories = Category::query()
            ->whereNull('parent_id')
            ->where('status', true)
            ->get();
        foreach ($mainCategories as $category) {
            $tabs[$category->id] = Tab::make($category->name)
                ->query(
                    fn($query) => $query->whereRelation('category', 'parent_id', $category->id),
                );
        }
        return $tabs;
    }
}
