<?php

namespace App\Filament\Resources\CategoryResource\Pages;

use App\Filament\Resources\CategoryResource;
use App\Models\Category;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;

class ListCategories extends ListRecords
{
    protected static string $resource = CategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
    public function getTabs(): array
    {
        $tabs = [
            0 => Tab::make(__('main_categories'))
                ->query(
                    fn($query) => $query->whereNull('parent_id'),
                )->badge(Category::query()->whereNull('parent_id')->count()),
        ];
        $mainCategories = Category::query()
            ->whereNull('parent_id')
            ->withCount(['subcategories'])
            ->get();
        foreach ($mainCategories as $category) {
            $tabs[$category->id] = Tab::make($category->name)
                ->query(
                    fn($query) => $query->where('parent_id', $category->id),
                )->badge($category->subcategories_count);
        }
        return $tabs;
    }
}
