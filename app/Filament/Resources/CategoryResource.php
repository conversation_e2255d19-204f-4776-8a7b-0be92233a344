<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CategoryResource\Pages;
use App\Models\Category;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class CategoryResource extends Resource
{
    protected static ?string $model = Category::class;

    protected static ?string $navigationIcon = 'heroicon-o-folder';

    public static function getNavigationLabel(): string
    {
        return __('categories');
    }

    public static function getModelLabel(): string
    {
        return __('category');
    }

    public static function getPluralModelLabel(): string
    {
        return __('categories');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label(__('name'))
                    ->placeholder(__('category_name_placeholder'))
                    ->helperText(__('category_name_help'))
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('parent_id')
                    ->label(__('parent_category'))
                    ->placeholder(__('select_parent_category'))
                    ->helperText(__('parent_category_help'))
                    ->relationship('parent', 'name')
                    ->searchable()
                    ->preload()
                    ->rules([
                        function () {
                            return function (string $attribute, $value, \Closure $fail) {
                                if (!$value) return; // Allow null parent

                                $currentId = request()->route('record');
                                if ($currentId && $value == $currentId) {
                                    $fail(__('category_cannot_be_own_parent'));
                                    return;
                                }

                                // Check for circular reference
                                if ($currentId) {
                                    $category = Category::find($currentId);
                                    if ($category) {
                                        $ancestors = $category->getAncestors();
                                        if ($ancestors->contains('id', $value)) {
                                            $fail(__('circular_reference_detected'));
                                        }
                                    }
                                }
                            };
                        }
                    ]),
                Forms\Components\Toggle::make('status')
                    ->label(__('status'))
                    ->helperText(__('category_status_help'))
                    ->default(true),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('id'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('name'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('parent.name')
                    ->label(__('parent_category'))
                    ->placeholder(__('no_parent'))
                    ->badge()
                    ->sortable(),
                Tables\Columns\IconColumn::make('status')
                    ->label(__('status'))
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('subcategories_count')
                    ->label(__('subcategories_count'))
                    ->counts('subcategories')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('parent_id')
                    ->label(__('parent_category'))
                    ->relationship('parent', 'name')
                    ->searchable()
                    ->preload(),
                Tables\Filters\Filter::make('parent_categories')
                    ->query(fn(Builder $query): Builder => $query->whereNull('parent_id'))
                    ->label(__('parent_categories_only')),
                Tables\Filters\Filter::make('subcategories')
                    ->query(fn(Builder $query): Builder => $query->whereNotNull('parent_id'))
                    ->label(__('subcategories_only')),
                Tables\Filters\Filter::make('active')
                    ->query(fn(Builder $query): Builder => $query->where('status', true))
                    ->label(__('active_filter')),
                Tables\Filters\Filter::make('inactive')
                    ->query(fn(Builder $query): Builder => $query->where('status', false))
                    ->label(__('inactive_filter')),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->modalHeading(__('delete_confirmation'))
                    ->successNotificationTitle(__('category_deleted')),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->modalHeading(__('bulk_delete_confirmation'))
                        ->successNotificationTitle(__('categories_bulk_deleted')),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCategories::route('/'),
            'create' => Pages\CreateCategory::route('/create'),
            'edit' => Pages\EditCategory::route('/{record}/edit'),
        ];
    }
}
