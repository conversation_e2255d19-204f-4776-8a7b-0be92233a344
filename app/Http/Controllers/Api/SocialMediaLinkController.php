<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\SocialMediaLinkResource;
use App\Models\SocialMediaLink;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SocialMediaLinkController extends Controller
{
    /**
     * Get all active social media links ordered by sort field.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getSocialMediaLinks(Request $request): JsonResponse
    {
        try {
            // Get active social media links ordered by sort field
            $socialMediaLinks = SocialMediaLink::query()
                ->active()
                ->ordered()
                ->get();

            return response()->json(
                SocialMediaLinkResource::collection($socialMediaLinks),
                200
            );
        } catch (\Exception $e) {
            Log::error('Error retrieving social media links: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all(),
            ]);

            return response()->json([
                'error' => 'An error occurred while retrieving social media links',
            ], 500);
        }
    }
}
