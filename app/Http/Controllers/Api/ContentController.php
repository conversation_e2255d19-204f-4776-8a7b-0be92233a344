<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\CategoryContentCollection;
use App\Http\Resources\CategoryResource;
use App\Http\Resources\ApiContentResource;
use App\Models\Category;
use App\Models\Content;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class ContentController extends Controller
{
    /**
     * Get all content items belonging to a specific category.
     *
     * @param Request $request
     * @param int $categoryId
     * @return JsonResponse
     */
    public function getCategoryContents(Request $request, int $categoryId): JsonResponse
    {
        try {
            // Validate query parameters
            $validated = $request->validate([
                'per_page' => 'integer|min:1|max:100',
                'page' => 'integer|min:1',
            ]);

            // Check if category exists and is active
            $category = Category::where('id', $categoryId)
                ->where('status', true)
                ->first();

            if (!$category) {
                return response()->json([
                    'error' => 'Category not found or inactive',
                ], 404);
            }

            // Set pagination parameters
            $perPage = $validated['per_page'] ?? 15;
            $page = $validated['page'] ?? 1;

            // Get content items for this category with their contentable models
            $contents = Content::query()
                ->where('category_id', $categoryId)
                ->where('status', true)
                ->with(['contentable'])
                ->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            return response()->json(
                new CategoryContentCollection($contents, $category),
                200
            );
        } catch (ValidationException $e) {
            return response()->json([
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error retrieving category contents: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all(),
                'category_id' => $categoryId,
            ]);

            return response()->json([
                'error' => 'An error occurred while retrieving content items',
            ], 500);
        }
    }

    /**
     * Search for content across all categories and content types.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function searchContent(Request $request): JsonResponse
    {
        try {
            // Validate query parameters
            $validated = $request->validate([
                'query' => 'required|string|min:1|max:255',
            ]);

            $searchQuery = trim($validated['query']);

            if (empty($searchQuery)) {
                return response()->json([
                    'error' => 'Search query cannot be empty',
                ], 422);
            }

            // Search across content titles, descriptions, and contentable models
            $contents = Content::query()
                ->where('status', true)
                ->where(function ($query) use ($searchQuery) {
                    // Search in content table
                    $query->where('title', 'LIKE', "%{$searchQuery}%")
                        ->orWhere('description', 'LIKE', "%{$searchQuery}%")
                        // Search in audio files
                        ->orWhereHasMorph('contentable', 'audio', function ($audioQuery) use ($searchQuery) {
                            $audioQuery->where('audio_file', 'LIKE', "%{$searchQuery}%");
                        })
                        // Search in video youtube IDs
                        ->orWhereHasMorph('contentable', 'video', function ($videoQuery) use ($searchQuery) {
                            $videoQuery->where('youtube_video_id', 'LIKE', "%{$searchQuery}%");
                        })
                        // Search in book publishers
                        ->orWhereHasMorph('contentable', 'book', function ($bookQuery) use ($searchQuery) {
                            $bookQuery->where('publisher', 'LIKE', "%{$searchQuery}%");
                        })
                        // Search in article text
                        ->orWhereHasMorph('contentable', 'article', function ($articleQuery) use ($searchQuery) {
                            $articleQuery->where('text', 'LIKE', "%{$searchQuery}%");
                        })
                        // Search in post text and URLs
                        ->orWhereHasMorph('contentable', 'post', function ($postQuery) use ($searchQuery) {
                            $postQuery->where('text', 'LIKE', "%{$searchQuery}%")
                                ->orWhere('url', 'LIKE', "%{$searchQuery}%");
                        });
                })
                ->with(['category', 'contentable'])
                ->whereHas('category', function ($query) {
                    $query->where('status', true);
                })
                ->orderBy('created_at', 'desc')
                ->get();

            // Group results by category
            $groupedResults = $contents->groupBy('category_id')->map(function ($categoryContents, $categoryId) {
                $category = $categoryContents->first()->category;

                return [
                    'category' => new CategoryResource($category),
                    'contents' => ApiContentResource::collection($categoryContents),
                    'content_count' => $categoryContents->count(),
                ];
            })->values();

            // Get total counts by content type

            $response = [
                'search_query' => $searchQuery,
                'total_results' => $contents->count(),
                'categories_with_results' => $groupedResults->count(),
                'results' => $groupedResults,
            ];

            return response()->json($response, 200);
        } catch (ValidationException $e) {
            return response()->json([
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error('Error searching content: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all(),
            ]);

            return response()->json([
                'error' => 'An error occurred while searching content',
            ], 500);
        }
    }
}
