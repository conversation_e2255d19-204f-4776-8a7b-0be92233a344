<?php

namespace App\Http\Resources;

use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;

class CategoryContentCollection extends ResourceCollection
{
    /**
     * Create a new resource collection instance.
     *
     * @param LengthAwarePaginator $resource
     * @param Category $category
     */
    public function __construct($resource)
    {
        parent::__construct($resource);
    }

    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => ApiContentResource::collection($this->collection),
            'pagination' => [
                'current_page' => $this->currentPage(),
                'per_page' => $this->perPage(),
                'total' => $this->total(),
                'last_page' => $this->lastPage(),
                'from' => $this->firstItem(),
                'to' => $this->lastItem(),
                'has_more' => $this->hasMorePages(),
            ],
        ];
    }

    /**
     * Get a summary of content types in this category.
     *
     * @return array
     */
    private function getContentTypeSummary(): array
    {
        $summary = [
            'audio_count' => 0,
            'video_count' => 0,
            'book_count' => 0,
            'total_count' => 0,
        ];

        foreach ($this->collection as $content) {
            $summary['total_count']++;

            match ($content->contentable_type) {
                'audio' => $summary['audio_count']++,
                'video' => $summary['video_count']++,
                'book' => $summary['book_count']++,
                default => null,
            };
        }

        return $summary;
    }
}
