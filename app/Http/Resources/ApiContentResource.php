<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ApiContentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'content_type_label' => $this->getContentTypeName(),
            'content_type' => $this->contentable_type,
            "$this->contentable_type" . "_data" => $this->formatContentableData(),
            'category_id' => $this->category_id,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }

    /**
     * Format the contentable data based on its type.
     *
     * @return array|null
     */
    private function formatContentableData(): ?array
    {
        if (!$this->contentable) {
            return null;
        }

        $baseData = [
            'id' => $this->contentable->id,
            'created_at' => $this->contentable->created_at?->toISOString(),
            'updated_at' => $this->contentable->updated_at?->toISOString(),
        ];

        return match ($this->contentable_type) {
            'audio' => array_merge($baseData, [
                'audio_file' => $this->contentable->audio_file,
                'audio_url' => $this->contentable->audio_file ? url('uploads/' . $this->contentable->audio_file) : null,
                'image' => $this->contentable->image,
                'image_url' => $this->contentable->image_url,
                'duration' => $this->contentable->duration,
            ]),
            'video' => array_merge($baseData, [
                'youtube_video_id' => $this->contentable->youtube_video_id,
                'duration' => $this->contentable->duration,
                'thumbnail_url' => $this->contentable->thumbnail_url,
            ]),
            'book' => array_merge($baseData, [
                'pages_count' => $this->contentable->pages_count,
                'published_date' => $this->contentable->published_date?->toDateString(),
                'publisher' => $this->contentable->publisher,
                'cover_image' => $this->contentable->cover_image,
                'cover_image_url' => $this->contentable->cover_image_url,
                'file' => $this->contentable->file,
                'file_url' => $this->contentable->file_url,
            ]),
            'article' => array_merge($baseData, [
                'text' => $this->contentable->text,
                'image' => $this->contentable->image,
                'image_url' => $this->contentable->image_url,
            ]),
            'post' => array_merge($baseData, [
                'text' => $this->contentable->text,
                'image' => $this->contentable->image,
                'image_url' => $this->contentable->image_url,
                'published_at' => $this->contentable->published_at?->toISOString(),
                'url' => $this->contentable->url,
            ]),
            default => $baseData,
        };
    }
}
