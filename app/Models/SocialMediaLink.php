<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class SocialMediaLink extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'key',
        'name',
        'icon',
        'url',
        'sort',
        'active',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'sort' => 'integer',
            'active' => 'boolean',
        ];
    }

    /**
     * Scope a query to only include active social media links.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope a query to order social media links by sort field.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort', 'asc');
    }

    /**
     * Get the icon URL.
     */
    public function iconUrl(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->icon ? url('uploads/' . $this->icon) : null
        );
    }

    /**
     * Check if the social media link is active.
     */
    public function isActive(): bool
    {
        return $this->active === true;
    }

    /**
     * Get a formatted display name for the social media platform.
     */
    public function getDisplayName(): string
    {
        return $this->name ?: ucfirst($this->key);
    }

    /**
     * Validation rules for the model.
     */
    public static function validationRules(): array
    {
        return [
            'key' => 'required|string|max:50|unique:social_media_links,key',
            'name' => 'required|string|max:100',
            'icon' => 'nullable|string|max:255',
            'url' => 'required|url|max:500',
            'sort' => 'integer|min:0|max:999',
            'active' => 'boolean',
        ];
    }

    /**
     * Validation rules for updating the model.
     */
    public static function updateValidationRules($id): array
    {
        return [
            'key' => 'required|string|max:50|unique:social_media_links,key,' . $id,
            'name' => 'required|string|max:100',
            'icon' => 'nullable|string|max:255',
            'url' => 'required|url|max:500',
            'sort' => 'integer|min:0|max:999',
            'active' => 'boolean',
        ];
    }
}
