<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Audio>
 */
class AudioFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'audio_file' => 'audio/' . $this->faker->uuid() . '.mp3',
            'image' => $this->faker->boolean(60) ? 'audio/thumbnails/' . $this->faker->uuid() . '.jpg' : null,
            'duration' => $this->faker->optional(0.8)->randomElement([
                '00:02:30',
                '00:03:45',
                '00:05:20',
                '00:08:15',
                '00:10:30',
                '00:12:45',
                '00:15:20',
                '01:02:30',
                '01:15:45',
            ]),
        ];
    }

    /**
     * Create an audio with an image.
     */
    public function withImage(): static
    {
        return $this->state(fn() => [
            'image' => 'audio/thumbnails/' . $this->faker->uuid() . '.jpg',
        ]);
    }

    /**
     * Create an audio without an image.
     */
    public function withoutImage(): static
    {
        return $this->state(fn() => [
            'image' => null,
        ]);
    }

    /**
     * Create an audio with a specific duration.
     */
    public function withDuration(string $duration): static
    {
        return $this->state(fn() => [
            'duration' => $duration,
        ]);
    }
}
