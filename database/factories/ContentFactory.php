<?php

namespace Database\Factories;

use App\Models\Article;
use App\Models\Audio;
use App\Models\Book;
use App\Models\Category;
use App\Models\Post;
use App\Models\Video;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Content>
 */
class ContentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $contentableTypes = ['audio', 'video', 'book', 'article', 'post'];
        $contentableType = $this->faker->randomElement($contentableTypes);

        // Map morph types to model classes for factory creation
        $modelClasses = [
            'audio' => Audio::class,
            'video' => Video::class,
            'book' => Book::class,
            'article' => Article::class,
            'post' => Post::class,
        ];

        return [
            'title' => $this->faker->sentence(3),
            'description' => $this->faker->paragraph(),
            'status' => $this->faker->boolean(80),
            'category_id' => Category::factory(),
            'contentable_type' => $contentableType,
            'contentable_id' => $modelClasses[$contentableType]::factory(),
        ];
    }

    /**
     * Create content for audio.
     */
    public function audio(): static
    {
        return $this->state(fn() => [
            'contentable_type' => 'audio',
            'contentable_id' => Audio::factory(),
        ]);
    }

    /**
     * Create content for video.
     */
    public function video(): static
    {
        return $this->state(fn() => [
            'contentable_type' => 'video',
            'contentable_id' => Video::factory(),
        ]);
    }

    /**
     * Create content for book.
     */
    public function book(): static
    {
        return $this->state(fn() => [
            'contentable_type' => 'book',
            'contentable_id' => Book::factory(),
        ]);
    }

    /**
     * Create content for article.
     */
    public function article(): static
    {
        return $this->state(fn() => [
            'contentable_type' => 'article',
            'contentable_id' => Article::factory(),
        ]);
    }

    /**
     * Create content for post.
     */
    public function post(): static
    {
        return $this->state(fn() => [
            'contentable_type' => 'post',
            'contentable_id' => Post::factory(),
        ]);
    }
}
