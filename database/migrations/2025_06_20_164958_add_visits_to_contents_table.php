<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('contents', function (Blueprint $table) {
            $table->unsignedInteger('visits')->default(0)->after('status');

            // Add index for performance when sorting by visits
            $table->index('visits');
            $table->index(['status', 'visits']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('contents', function (Blueprint $table) {
            $table->dropIndex(['contents_visits_index']);
            $table->dropIndex(['contents_status_visits_index']);
            $table->dropColumn('visits');
        });
    }
};
