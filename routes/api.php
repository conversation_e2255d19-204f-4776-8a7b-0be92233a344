<?php

use App\Http\Controllers\Api\AudioController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\ContentController;
use App\Http\Controllers\Api\SocialMediaLinkController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// API v1 routes
Route::prefix('v1')->group(function () {
    // Category endpoints
    Route::get('/categories', [CategoryController::class, 'getCategories']);
    Route::get('/categories/{category}/contents', [ContentController::class, 'getCategoryContents']);

    // Content search endpoint
    Route::get('/content/search', [ContentController::class, 'searchContent']);

    // Content visit tracking endpoint
    Route::post('/content/{id}/visit', [ContentController::class, 'incrementVisit']);

    // Social media links endpoint
    Route::get('/social-media-links', [SocialMediaLinkController::class, 'getSocialMediaLinks']);
});
